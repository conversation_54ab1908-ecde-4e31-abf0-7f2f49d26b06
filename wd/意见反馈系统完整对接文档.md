# 意见反馈系统完整对接文档

## 1. 数据库设计

### 1.1 主表结构

```sql
-- 意见反馈主表
CREATE TABLE `feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '反馈ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID（如果已登录）',
  `wechat_openid` varchar(100) DEFAULT NULL COMMENT '微信OpenID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名（冗余字段，便于查询）',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名（冗余字段）',
  `feedback_type` varchar(20) NOT NULL COMMENT '反馈类型：建议、问题反馈、功能需求、其他',
  `content` text NOT NULL COMMENT '反馈内容',
  `contact_info` varchar(200) DEFAULT NULL COMMENT '联系方式（可选）',
  `status` enum('pending','processing','resolved','closed') DEFAULT 'pending' COMMENT '处理状态：待处理、处理中、已解决、已关闭',
  `priority` enum('low','medium','high','urgent') DEFAULT 'medium' COMMENT '优先级',
  `admin_reply` text DEFAULT NULL COMMENT '管理员回复',
  `admin_id` int(11) DEFAULT NULL COMMENT '处理的管理员ID',
  `admin_name` varchar(50) DEFAULT NULL COMMENT '处理管理员姓名',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `resolved_at` timestamp NULL DEFAULT NULL COMMENT '解决时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_feedback_type` (`feedback_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='意见反馈表';
```

### 1.2 扩展表（可选）

```sql
-- 反馈附件表（预留扩展）
CREATE TABLE `feedback_attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feedback_id` int(11) NOT NULL COMMENT '反馈ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_feedback_id` (`feedback_id`),
  FOREIGN KEY (`feedback_id`) REFERENCES `feedback` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='反馈附件表';
```

## 2. API接口设计

### 2.1 接口基础信息

- **基础URL**: `https://sunxiyue.com/zdh/api/feedback_api.php`
- **请求方式**: GET/POST
- **响应格式**: JSON
- **字符编码**: UTF-8

### 2.2 接口列表

#### 2.2.1 提交意见反馈

**接口说明**: 用户提交意见反馈

**请求方式**: POST

**请求参数**:
```json
{
  "action": "submit_feedback",
  "feedback_type": "建议",
  "content": "这是反馈内容",
  "contact_info": "可选的联系方式",
  "wechat_auth": true,
  "user_info": {
    "id": 123,
    "username": "testuser",
    "real_name": "张三",
    "openid": "wx_openid_123"
  }
}
```

**参数说明**:
- `action`: 操作类型，固定值 "submit_feedback"
- `feedback_type`: 反馈类型，可选值：建议、问题反馈、功能需求、其他
- `content`: 反馈内容，必填，最大500字符
- `contact_info`: 联系方式，可选，最大200字符
- `wechat_auth`: 是否微信认证，布尔值
- `user_info`: 用户信息对象，如果已登录则传递

**响应格式**:
```json
{
  "success": true,
  "message": "反馈提交成功",
  "data": {
    "feedback_id": 1,
    "created_at": "2024-01-01 12:00:00"
  }
}
```

#### 2.2.2 获取反馈列表（管理员）

**接口说明**: 管理员获取反馈列表

**请求方式**: GET

**请求参数**:
```
action=get_feedback_list
&page=1
&limit=20
&status=pending
&type=建议
&search=关键词
&admin_token=管理员token
```

**参数说明**:
- `action`: 操作类型，固定值 "get_feedback_list"
- `page`: 页码，默认1
- `limit`: 每页数量，默认20，最大100
- `status`: 状态筛选，可选值：pending、processing、resolved、closed
- `type`: 类型筛选，可选值：建议、问题反馈、功能需求、其他
- `search`: 搜索关键词，搜索内容和联系方式
- `admin_token`: 管理员token，必填

**响应格式**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "user_info": {
          "id": 123,
          "username": "testuser",
          "real_name": "张三"
        },
        "feedback_type": "建议",
        "content": "反馈内容",
        "contact_info": "联系方式",
        "status": "pending",
        "status_text": "待处理",
        "priority": "medium",
        "priority_text": "中等",
        "admin_reply": null,
        "admin_name": null,
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:00:00",
        "resolved_at": null
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20,
    "total_pages": 5
  }
}
```

#### 2.2.3 获取反馈详情（管理员）

**接口说明**: 管理员获取单个反馈详情

**请求方式**: GET

**请求参数**:
```
action=get_feedback_detail
&feedback_id=1
&admin_token=管理员token
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "user_info": {
      "id": 123,
      "username": "testuser",
      "real_name": "张三"
    },
    "feedback_type": "建议",
    "content": "详细的反馈内容...",
    "contact_info": "联系方式",
    "status": "pending",
    "priority": "medium",
    "admin_reply": null,
    "admin_name": null,
    "created_at": "2024-01-01 12:00:00",
    "updated_at": "2024-01-01 12:00:00",
    "resolved_at": null
  }
}
```

#### 2.2.4 更新反馈状态（管理员）

**接口说明**: 管理员更新反馈状态和回复

**请求方式**: POST

**请求参数**:
```json
{
  "action": "update_feedback",
  "feedback_id": 1,
  "status": "resolved",
  "priority": "high",
  "admin_reply": "感谢您的建议，我们会考虑实现",
  "admin_token": "管理员token"
}
```

**参数说明**:
- `feedback_id`: 反馈ID，必填
- `status`: 状态，可选值：pending、processing、resolved、closed
- `priority`: 优先级，可选值：low、medium、high、urgent
- `admin_reply`: 管理员回复，可选
- `admin_token`: 管理员token，必填

**响应格式**:
```json
{
  "success": true,
  "message": "更新成功",
  "data": {
    "feedback_id": 1,
    "updated_at": "2024-01-01 12:30:00"
  }
}
```

#### 2.2.5 获取反馈统计（管理员）

**接口说明**: 管理员获取反馈统计数据

**请求方式**: GET

**请求参数**:
```
action=get_feedback_stats
&admin_token=管理员token
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "total": 150,
    "pending": 25,
    "processing": 10,
    "resolved": 100,
    "closed": 15,
    "by_type": {
      "建议": 60,
      "问题反馈": 40,
      "功能需求": 30,
      "其他": 20
    },
    "by_priority": {
      "low": 50,
      "medium": 70,
      "high": 25,
      "urgent": 5
    },
    "recent_7_days": 12,
    "recent_30_days": 45
  }
}
```

#### 2.2.6 删除反馈（管理员）

**接口说明**: 管理员删除反馈记录

**请求方式**: POST

**请求参数**:
```json
{
  "action": "delete_feedback",
  "feedback_id": 1,
  "admin_token": "管理员token"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "删除成功"
}
```

## 3. 前端代码修改

### 3.1 修改提交逻辑

将 `pages/feedback/index.js` 中的 `onSubmit` 方法替换为：

```javascript
// 提交反馈
onSubmit: function() {
  if (!this.data.feedbackContent.trim()) {
    wx.showToast({
      title: '请输入反馈内容',
      icon: 'none'
    });
    return;
  }

  if (this.data.feedbackContent.trim().length > 500) {
    wx.showToast({
      title: '反馈内容不能超过500字',
      icon: 'none'
    });
    return;
  }

  wx.showLoading({
    title: '提交中...'
  });

  // 获取用户信息
  const userInfo = wx.getStorageSync('user_info');
  const wechatToken = wx.getStorageSync('wechat_token');

  // 提交反馈
  wx.request({
    url: 'https://sunxiyue.com/zdh/api/feedback_api.php',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data: {
      action: 'submit_feedback',
      feedback_type: this.data.feedbackType,
      content: this.data.feedbackContent.trim(),
      contact_info: this.data.contactInfo.trim(),
      wechat_auth: !!wechatToken,
      user_info: userInfo || null
    },
    success: (res) => {
      wx.hideLoading();
      if (res.data && res.data.success) {
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        });
        
        // 清空表单
        this.setData({
          feedbackContent: '',
          contactInfo: '',
          feedbackType: '建议'
        });
      } else {
        wx.showToast({
          title: res.data?.message || '提交失败',
          icon: 'none'
        });
      }
    },
    fail: (error) => {
      wx.hideLoading();
      console.error('提交反馈失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    }
  });
}
```

## 4. 后端API实现

### 4.1 创建 feedback_api.php 文件

```php
<?php
/**
 * 意见反馈API
 * 提供意见反馈的提交和管理功能
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 引入数据库配置
require_once 'config.php';

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';

    switch ($action) {
        case 'submit_feedback':
            handleSubmitFeedback();
            break;

        case 'get_feedback_list':
            handleGetFeedbackList();
            break;

        case 'get_feedback_detail':
            handleGetFeedbackDetail();
            break;

        case 'update_feedback':
            handleUpdateFeedback();
            break;

        case 'get_feedback_stats':
            handleGetFeedbackStats();
            break;

        case 'delete_feedback':
            handleDeleteFeedback();
            break;

        default:
            throw new Exception('无效的操作');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 提交意见反馈
 */
function handleSubmitFeedback() {
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    $feedbackType = $input['feedback_type'] ?? '';
    $content = $input['content'] ?? '';
    $contactInfo = $input['contact_info'] ?? '';
    $wechatAuth = $input['wechat_auth'] ?? false;
    $userInfo = $input['user_info'] ?? null;

    // 验证必填字段
    if (empty($feedbackType) || empty($content)) {
        throw new Exception('反馈类型和内容不能为空');
    }

    // 验证反馈类型
    $validTypes = ['建议', '问题反馈', '功能需求', '其他'];
    if (!in_array($feedbackType, $validTypes)) {
        throw new Exception('无效的反馈类型');
    }

    // 验证内容长度
    if (mb_strlen($content) > 500) {
        throw new Exception('反馈内容不能超过500字');
    }

    // 准备插入数据
    $userId = null;
    $wechatOpenid = null;
    $username = null;
    $realName = null;

    if ($wechatAuth && $userInfo) {
        $userId = $userInfo['id'] ?? null;
        $wechatOpenid = $userInfo['openid'] ?? null;
        $username = $userInfo['username'] ?? null;
        $realName = $userInfo['real_name'] ?? null;
    }

    // 插入数据库
    $stmt = $pdo->prepare("
        INSERT INTO feedback (
            user_id, wechat_openid, username, real_name,
            feedback_type, content, contact_info, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");

    $stmt->execute([
        $userId, $wechatOpenid, $username, $realName,
        $feedbackType, $content, $contactInfo
    ]);

    $feedbackId = $pdo->lastInsertId();

    echo json_encode([
        'success' => true,
        'message' => '反馈提交成功',
        'data' => [
            'feedback_id' => $feedbackId,
            'created_at' => date('Y-m-d H:i:s')
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取反馈列表（管理员）
 */
function handleGetFeedbackList() {
    global $pdo;

    // 验证管理员权限
    $adminToken = $_GET['admin_token'] ?? '';
    if (!verifyAdminToken($adminToken)) {
        throw new Exception('权限不足');
    }

    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(100, max(1, intval($_GET['limit'] ?? 20)));
    $status = $_GET['status'] ?? '';
    $type = $_GET['type'] ?? '';
    $search = $_GET['search'] ?? '';

    $offset = ($page - 1) * $limit;

    // 构建查询条件
    $where = [];
    $params = [];

    if ($status) {
        $where[] = "status = ?";
        $params[] = $status;
    }

    if ($type) {
        $where[] = "feedback_type = ?";
        $params[] = $type;
    }

    if ($search) {
        $where[] = "(content LIKE ? OR contact_info LIKE ? OR username LIKE ? OR real_name LIKE ?)";
        $searchTerm = "%{$search}%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

    // 获取总数
    $countSql = "SELECT COUNT(*) FROM feedback {$whereClause}";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total = $countStmt->fetchColumn();

    // 获取列表
    $sql = "
        SELECT id, user_id, username, real_name, feedback_type, content,
               contact_info, status, priority, admin_reply, admin_name,
               created_at, updated_at, resolved_at
        FROM feedback
        {$whereClause}
        ORDER BY created_at DESC
        LIMIT {$limit} OFFSET {$offset}
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $list = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 格式化数据
    foreach ($list as &$item) {
        $item['status_text'] = getStatusText($item['status']);
        $item['priority_text'] = getPriorityText($item['priority']);
        $item['user_info'] = [
            'id' => $item['user_id'],
            'username' => $item['username'],
            'real_name' => $item['real_name']
        ];
        unset($item['user_id'], $item['username'], $item['real_name']);
    }

    echo json_encode([
        'success' => true,
        'data' => [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($total / $limit)
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取反馈详情（管理员）
 */
function handleGetFeedbackDetail() {
    global $pdo;

    // 验证管理员权限
    $adminToken = $_GET['admin_token'] ?? '';
    if (!verifyAdminToken($adminToken)) {
        throw new Exception('权限不足');
    }

    $feedbackId = $_GET['feedback_id'] ?? '';
    if (!$feedbackId) {
        throw new Exception('反馈ID不能为空');
    }

    $stmt = $pdo->prepare("
        SELECT id, user_id, username, real_name, feedback_type, content,
               contact_info, status, priority, admin_reply, admin_name,
               created_at, updated_at, resolved_at
        FROM feedback
        WHERE id = ?
    ");

    $stmt->execute([$feedbackId]);
    $feedback = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$feedback) {
        throw new Exception('反馈不存在');
    }

    // 格式化数据
    $feedback['status_text'] = getStatusText($feedback['status']);
    $feedback['priority_text'] = getPriorityText($feedback['priority']);
    $feedback['user_info'] = [
        'id' => $feedback['user_id'],
        'username' => $feedback['username'],
        'real_name' => $feedback['real_name']
    ];
    unset($feedback['user_id'], $feedback['username'], $feedback['real_name']);

    echo json_encode([
        'success' => true,
        'data' => $feedback
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 更新反馈状态（管理员）
 */
function handleUpdateFeedback() {
    global $pdo;

    // 验证管理员权限
    $input = json_decode(file_get_contents('php://input'), true);
    $adminToken = $input['admin_token'] ?? '';
    $adminInfo = verifyAdminToken($adminToken);
    if (!$adminInfo) {
        throw new Exception('权限不足');
    }

    $feedbackId = $input['feedback_id'] ?? '';
    $status = $input['status'] ?? '';
    $priority = $input['priority'] ?? '';
    $adminReply = $input['admin_reply'] ?? '';

    if (!$feedbackId) {
        throw new Exception('反馈ID不能为空');
    }

    // 验证状态和优先级
    $validStatuses = ['pending', 'processing', 'resolved', 'closed'];
    $validPriorities = ['low', 'medium', 'high', 'urgent'];

    $updateFields = [];
    $params = [];

    if ($status && in_array($status, $validStatuses)) {
        $updateFields[] = "status = ?";
        $params[] = $status;

        if ($status === 'resolved') {
            $updateFields[] = "resolved_at = NOW()";
        }
    }

    if ($priority && in_array($priority, $validPriorities)) {
        $updateFields[] = "priority = ?";
        $params[] = $priority;
    }

    if ($adminReply !== '') {
        $updateFields[] = "admin_reply = ?";
        $updateFields[] = "admin_id = ?";
        $updateFields[] = "admin_name = ?";
        $params[] = $adminReply;
        $params[] = $adminInfo['user_id'];
        $params[] = $adminInfo['real_name'] ?: $adminInfo['username'];
    }

    if (empty($updateFields)) {
        throw new Exception('没有要更新的字段');
    }

    $updateFields[] = "updated_at = NOW()";
    $params[] = $feedbackId;

    $sql = "UPDATE feedback SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);

    echo json_encode([
        'success' => true,
        'message' => '更新成功',
        'data' => [
            'feedback_id' => $feedbackId,
            'updated_at' => date('Y-m-d H:i:s')
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取反馈统计（管理员）
 */
function handleGetFeedbackStats() {
    global $pdo;

    // 验证管理员权限
    $adminToken = $_GET['admin_token'] ?? '';
    if (!verifyAdminToken($adminToken)) {
        throw new Exception('权限不足');
    }

    // 总体统计
    $stmt = $pdo->query("
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
            SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved,
            SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed
        FROM feedback
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // 按类型统计
    $stmt = $pdo->query("
        SELECT feedback_type, COUNT(*) as count
        FROM feedback
        GROUP BY feedback_type
    ");
    $typeStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $byType = [];
    foreach ($typeStats as $item) {
        $byType[$item['feedback_type']] = intval($item['count']);
    }

    // 按优先级统计
    $stmt = $pdo->query("
        SELECT priority, COUNT(*) as count
        FROM feedback
        GROUP BY priority
    ");
    $priorityStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $byPriority = [];
    foreach ($priorityStats as $item) {
        $byPriority[$item['priority']] = intval($item['count']);
    }

    // 最近7天和30天统计
    $stmt = $pdo->query("
        SELECT
            SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as recent_7_days,
            SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recent_30_days
        FROM feedback
    ");
    $recentStats = $stmt->fetch(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'data' => [
            'total' => intval($stats['total']),
            'pending' => intval($stats['pending']),
            'processing' => intval($stats['processing']),
            'resolved' => intval($stats['resolved']),
            'closed' => intval($stats['closed']),
            'by_type' => $byType,
            'by_priority' => $byPriority,
            'recent_7_days' => intval($recentStats['recent_7_days']),
            'recent_30_days' => intval($recentStats['recent_30_days'])
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 删除反馈（管理员）
 */
function handleDeleteFeedback() {
    global $pdo;

    // 验证管理员权限
    $input = json_decode(file_get_contents('php://input'), true);
    $adminToken = $input['admin_token'] ?? '';
    if (!verifyAdminToken($adminToken)) {
        throw new Exception('权限不足');
    }

    $feedbackId = $input['feedback_id'] ?? '';
    if (!$feedbackId) {
        throw new Exception('反馈ID不能为空');
    }

    $stmt = $pdo->prepare("DELETE FROM feedback WHERE id = ?");
    $stmt->execute([$feedbackId]);

    echo json_encode([
        'success' => true,
        'message' => '删除成功'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 验证管理员Token
 */
function verifyAdminToken($token) {
    global $pdo;

    if (empty($token)) {
        return false;
    }

    try {
        $stmt = $pdo->prepare("
            SELECT wt.user_id, u.username, u.role, u.real_name
            FROM wechat_tokens wt
            JOIN users u ON wt.user_id = u.id
            WHERE wt.token = ? AND wt.expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $result = $stmt->fetch();

        if ($result && $result['role'] === 'admin') {
            return $result;
        }

        return false;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 获取状态文本
 */
function getStatusText($status) {
    $statusMap = [
        'pending' => '待处理',
        'processing' => '处理中',
        'resolved' => '已解决',
        'closed' => '已关闭'
    ];
    return $statusMap[$status] ?? $status;
}

/**
 * 获取优先级文本
 */
function getPriorityText($priority) {
    $priorityMap = [
        'low' => '低',
        'medium' => '中等',
        'high' => '高',
        'urgent' => '紧急'
    ];
    return $priorityMap[$priority] ?? $priority;
}
?>
```

## 5. 后台管理页面设计

### 5.1 页面结构

建议创建以下管理页面：

1. **反馈列表页面** (`pages/feedback-management/index`)
   - 显示所有反馈列表
   - 支持筛选、搜索、分页
   - 快速操作按钮

2. **反馈详情页面** (`pages/feedback-management/detail`)
   - 显示反馈详细信息
   - 管理员回复功能
   - 状态和优先级修改

3. **反馈统计页面** (`pages/feedback-management/stats`)
   - 各种统计图表
   - 数据概览

### 5.2 权限控制

所有反馈管理页面都应该添加管理员权限检查：

```javascript
// 检查管理员权限
checkAdminPermission() {
  const userInfo = wx.getStorageSync('user_info');
  const token = wx.getStorageSync('wechat_token');

  if (!token || !userInfo || userInfo.role !== 'admin') {
    wx.showModal({
      title: '权限不足',
      content: '只有管理员可以访问反馈管理功能',
      showCancel: false,
      success: () => {
        wx.navigateBack();
      }
    });
    return false;
  }
  return true;
}
```

## 6. 部署清单

### 6.1 数据库操作
1. 执行SQL创建 `feedback` 表
2. 可选：创建 `feedback_attachments` 表

### 6.2 文件部署
1. 上传 `feedback_api.php` 到服务器 `/api/` 目录
2. 确保文件权限正确

### 6.3 前端修改
1. 修改 `pages/feedback/index.js` 的提交逻辑
2. 创建后台管理页面（可选）

### 6.4 测试验证
1. 测试反馈提交功能
2. 测试管理员查看和处理功能
3. 验证权限控制

## 7. 注意事项

1. **安全性**：所有管理员操作都需要验证token
2. **数据验证**：前后端都要进行数据验证
3. **错误处理**：完善的错误提示和日志记录
4. **性能优化**：大量数据时考虑分页和索引
5. **扩展性**：预留附件上传等功能的扩展空间

这个文档提供了完整的意见反馈系统对接方案，你可以按照这个文档逐步实现功能。
