<?php
/**
 * 倒班时间管理新API接口
 * 使用平台数据库和用户认证系统
 */

require_once __DIR__ . '/../includes/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 处理输入数据
$input = file_get_contents('php://input');
$contentType = $_SERVER['CONTENT_TYPE'] ?? '';

if ($contentType === 'application/json') {
    $_POST = json_decode($input, true) ?? [];
} else {
    parse_str($input, $parsed);
    $_POST = array_merge($_POST, $parsed);
}

$action = $_POST['action'] ?? $_GET['action'] ?? '';

// 创建必要的表结构
createRequiredTables();

// 获取客户端IP地址
function getClientIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// 创建必要的数据表
function createRequiredTables() {
    global $pdo;

    try {
        // 创建登录尝试表
        $pdo->exec("CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            attempt_time DATETIME NOT NULL,
            is_success TINYINT(1) DEFAULT 0,
            module VARCHAR(50) DEFAULT 'shift_schedule',
            INDEX idx_username_time (username, attempt_time),
            INDEX idx_ip_time (ip_address, attempt_time)
        )");
    } catch (PDOException $e) {
        // 创建表失败
    }
}

// 检查登录尝试次数，如果超过限制则锁定
function checkLoginAttempts($username) {
    global $pdo;

    $ip = getClientIP();
    $lockoutTime = 3600; // 1小时锁定时间（秒）
    $maxAttempts = 5; // 最大尝试次数

    try {
        // 检查IP锁定（只锁定IP，不锁定账号）
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM login_attempts
                              WHERE ip_address = ?
                              AND is_success = 0
                              AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stmt->execute([$ip]);
        $ipAttempts = $stmt->fetchColumn();

        if ($ipAttempts >= $maxAttempts) {
            $remainingTime = getRemainingLockTime($ip, 'ip_address');
            return [
                'locked' => true,
                'message' => "IP已锁定，请{$remainingTime}分钟后再试"
            ];
        }

        return ['locked' => false];
    } catch (PDOException $e) {
        return ['locked' => false];
    }
}

// 记录登录尝试
function recordLoginAttempt($username, $isSuccess = false) {
    global $pdo;

    $ip = getClientIP();

    try {
        $stmt = $pdo->prepare("INSERT INTO login_attempts (username, ip_address, attempt_time, is_success, module)
                              VALUES (?, ?, NOW(), ?, 'shift_schedule')");
        $stmt->execute([$username, $ip, $isSuccess ? 1 : 0]);
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// 重置登录尝试次数
function resetLoginAttempts($username) {
    global $pdo;

    $ip = getClientIP();

    try {
        // 只重置IP的失败记录
        $stmt = $pdo->prepare("UPDATE login_attempts
                                SET is_success = 1
                                WHERE ip_address = ? AND is_success = 0");
        $stmt->execute([$ip]);
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// 获取剩余锁定时间（分钟）
function getRemainingLockTime($value, $type = 'ip_address') {
    global $pdo;

    try {
        $column = ($type == 'username') ? 'username' : 'ip_address';

        $stmt = $pdo->prepare("SELECT
                                TIMESTAMPDIFF(SECOND, NOW(), DATE_ADD(MIN(attempt_time), INTERVAL 1 HOUR)) as remaining_seconds
                              FROM login_attempts
                              WHERE {$column} = ?
                              AND is_success = 0
                              AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stmt->execute([$value]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && $result['remaining_seconds'] > 0) {
            return ceil(abs($result['remaining_seconds']) / 60);
        }

        return 60;
    } catch (PDOException $e) {
        return 60;
    }
}

// 验证微信登录状态
function verifyWechatAuth() {
    // 检查是否有微信认证标识
    if (isset($_POST['wechat_auth']) && $_POST['wechat_auth'] === true) {
        $userInfo = $_POST['user_info'] ?? null;

        if (!$userInfo || !isset($userInfo['role'])) {
            die(json_encode(['status'=>'error','message'=>'微信用户信息无效']));
        }

        // 检查是否是管理员
        if ($userInfo['role'] !== 'admin') {
            die(json_encode(['status'=>'error','message'=>'权限不足，只有管理员可以操作']));
        }

        return $userInfo;
    }

    // 如果没有微信认证，尝试使用原来的token验证
    return verifyToken();
}

// Token 验证函数（保留原有逻辑）
function verifyToken() {
    global $pdo;

    $headers = getallheaders();
    if (!isset($headers['Authorization'])) {
        die(json_encode(['status'=>'error','message'=>'缺少授权头信息']));
    }

    $token = str_replace('Bearer ', '', $headers['Authorization']);
    
    try {
        // 清理过期Token
        $pdo->exec("DELETE FROM shift_tokens WHERE expires_at < NOW()");
        
        $stmt = $pdo->prepare("
            SELECT st.*, u.username, u.role  
            FROM shift_tokens st
            JOIN users u ON st.user_id = u.id  
            WHERE st.token = ? 
            AND st.expires_at > NOW()
            AND u.role = 'admin'
        ");
        $stmt->execute([$token]);
        $tokenData = $stmt->fetch();

        if (!$tokenData) {
            die(json_encode(['status'=>'error','message'=>'无效或已过期的令牌']));
        }

        // 更新Token有效期（如果剩余时间少于6小时则延长）
        $currentExpiry = strtotime($tokenData['expires_at']);
        if ($currentExpiry - time() < 21600) {
            $newExpiry = date('Y-m-d H:i:s', time() + 86400);
            $stmt = $pdo->prepare("
                UPDATE shift_tokens 
                SET expires_at = ? 
                WHERE id = ?
            ");
            $stmt->execute([$newExpiry, $tokenData['id']]);
        }
        
        return $tokenData;
    } catch(PDOException $e) {
        die(json_encode(['status'=>'error','message'=>'令牌验证失败']));
    }
}

// 记录操作日志
function logShiftAction($action, $details = []) {
    global $pdo;
    
    $userId = $_SESSION['user']['id'] ?? 0;
    
    try {
        $stmt = $pdo->prepare("INSERT INTO operation_logs (user_id, action, target, details) VALUES (?,?,?,?)");
        $stmt->execute([
            $userId,
            $action,
            'shift_schedule',
            json_encode($details, JSON_UNESCAPED_UNICODE)
        ]);
    } catch(PDOException $e) {
        // 日志记录失败
    }
}

// 接口路由
try {
    switch($action) {
        case 'login':
            // 小程序登录接口
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            
            if (empty($username) || empty($password)) {
                die(json_encode(['status'=>'error','message'=>'请输入用户名和密码']));
            }

            // 检查是否被锁定
            $lockStatus = checkLoginAttempts($username);
            if ($lockStatus['locked']) {
                recordLoginAttempt($username, false);
                die(json_encode(['status'=>'error','message'=>$lockStatus['message']]));
            }

            // 验证用户（只允许admin角色登录）
            $stmt = $pdo->prepare("SELECT id, username, role, password FROM users WHERE username = ? AND role = 'admin'");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if (!$user || !password_verify($password, $user['password'])) {
                recordLoginAttempt($username, false);
                die(json_encode(['status'=>'error','message'=>'用户名或密码错误，或无权限访问']));
            }

            // 登录成功，重置失败尝试记录
            resetLoginAttempts($username);
            recordLoginAttempt($username, true);

            // 记录操作日志
            if (function_exists('logAction')) {
                // 临时设置SESSION以便logAction能获取到用户ID
                $_SESSION['user'] = ['id' => $user['id'], 'username' => $user['username']];
                logAction('用户登录', 'login', ['username' => $username]);
                // 清除临时SESSION（API接口不需要保持SESSION）
                unset($_SESSION['user']);
            }

            // 生成Token
            $token = bin2hex(random_bytes(16));
            $expiresAt = date('Y-m-d\TH:i:s', time() + 86400);
            
            // 删除旧Token
            $stmt = $pdo->prepare("DELETE FROM shift_tokens WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            
            // 插入新Token
            $stmt = $pdo->prepare("
                INSERT INTO shift_tokens (user_id, token, expires_at) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$user['id'], $token, $expiresAt]);
            
            echo json_encode([
                'status' => 'success',
                'token' => $token,
                'expires_at' => $expiresAt,
                'user' => $user['username']
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            break;
            
        case 'get_data':
            // 获取排班数据
            $year = $_GET['year'] ?? date('Y');
            
            try {
                $stmt = $pdo->prepare("
                    SELECT id, year, month, 
                           DATE_FORMAT(start_date, '%Y-%m-%d') as start_date,
                           DATE_FORMAT(end_date, '%Y-%m-%d') as end_date,
                           station, days 
                    FROM shift_schedules 
                    WHERE year = ?
                    ORDER BY month ASC, start_date ASC
                ");
                $stmt->execute([$year]);
                $data = $stmt->fetchAll();
                
                echo json_encode(['status' => 'success', 'data' => $data]);
            } catch(PDOException $e) {
                die(json_encode([
                    'status' => 'error',
                    'message' => '获取数据失败'
                ]));
            }
            break;
            
        case 'add_schedule':
            // 添加排班（需要登录验证）
            $authUser = verifyWechatAuth();
            
            $data = $_POST;
            unset($data['action']);
            
            $required = ['station', 'startDate', 'endDate'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    die(json_encode(['status'=>'error','message'=>"缺少必填字段: $field"]));
                }
            }
            
            try {
                $start = new DateTime($data['startDate']);
                $end = new DateTime($data['endDate']);
                $days = $start->diff($end)->days + 1;
            } catch(Exception $e) {
                die(json_encode(['status'=>'error','message'=>'日期格式错误']));
            }
            
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO shift_schedules 
                    (year, month, station, start_date, end_date, days)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $start->format('Y'),
                    $start->format('n'),
                    $data['station'],
                    $data['startDate'],
                    $data['endDate'],
                    $days
                ]);

                // 获取新插入记录的ID
                $newId = $pdo->lastInsertId();

                // 设置SESSION以便logShiftAction能获取到用户ID
                if (isset($authUser['username'])) {
                    $_SESSION['user'] = [
                        'id' => $authUser['id'] ?? 0,
                        'username' => $authUser['username'],
                        'real_name' => $authUser['real_name'] ?? $authUser['username']
                    ];
                    // 添加新记录的ID到日志数据中
                    $logData = $data;
                    $logData['id'] = $newId;
                    logShiftAction('添加排班', $logData);
                    unset($_SESSION['user']);
                }

                echo json_encode(['status' => 'success', 'message' => '排班添加成功']);
            } catch(PDOException $e) {
                die(json_encode(['status'=>'error','message'=>'添加失败']));
            }
            break;
            
        case 'update_schedule':
            // 更新排班（需要登录验证）
            $authUser = verifyWechatAuth();
            
            $data = $_POST;
            unset($data['action']);
            
            if (!isset($data['id']) || !is_numeric($data['id'])) {
                die(json_encode(['status'=>'error','message'=>'无效的ID']));
            }
            
            try {
                $start = new DateTime($data['startDate']);
                $end = new DateTime($data['endDate']);
                $days = $start->diff($end)->days + 1;
            } catch(Exception $e) {
                die(json_encode(['status'=>'error','message'=>'日期格式错误']));
            }
            
            try {
                $stmt = $pdo->prepare("
                    UPDATE shift_schedules SET
                        year = ?,
                        month = ?,
                        station = ?,
                        start_date = ?,
                        end_date = ?,
                        days = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ");
                
                $stmt->execute([
                    $start->format('Y'),
                    $start->format('n'),
                    $data['station'],
                    $data['startDate'],
                    $data['endDate'],
                    $days,
                    $data['id']
                ]);

                // 设置SESSION以便logShiftAction能获取到用户ID
                if (isset($authUser['username'])) {
                    $_SESSION['user'] = [
                        'id' => $authUser['id'] ?? 0,
                        'username' => $authUser['username'],
                        'real_name' => $authUser['real_name'] ?? $authUser['username']
                    ];
                    logShiftAction('更新排班', $data);
                    unset($_SESSION['user']);
                }

                echo json_encode(['status' => 'success', 'message' => '排班更新成功']);
            } catch(PDOException $e) {
                die(json_encode(['status'=>'error','message'=>'更新失败']));
            }
            break;
            
        case 'delete_schedule':
            // 删除排班（需要登录验证）
            $authUser = verifyWechatAuth();
            
            $id = $_POST['id'] ?? $_GET['id'] ?? '';
            if (!is_numeric($id)) {
                die(json_encode(['status'=>'error','message'=>'无效的ID']));
            }
            
            try {
                $stmt = $pdo->prepare("DELETE FROM shift_schedules WHERE id = ?");
                $stmt->execute([$id]);

                // 设置SESSION以便logShiftAction能获取到用户ID
                if (isset($authUser['username'])) {
                    $_SESSION['user'] = [
                        'id' => $authUser['id'] ?? 0,
                        'username' => $authUser['username'],
                        'real_name' => $authUser['real_name'] ?? $authUser['username']
                    ];
                    logShiftAction('删除排班', ['id' => $id]);
                    unset($_SESSION['user']);
                }

                echo json_encode(['status' => 'success', 'message' => '排班删除成功']);
            } catch(PDOException $e) {
                die(json_encode(['status'=>'error','message'=>'删除失败']));
            }
            break;
            
        case 'get_remarks':
            // 获取备注
            $year = $_GET['year'] ?? date('Y');
            
            try {
                $stmt = $pdo->prepare("
                    SELECT month, remark 
                    FROM shift_month_remarks 
                    WHERE year = ?
                ");
                $stmt->execute([$year]);
                $remarks = [];
                while ($row = $stmt->fetch()) {
                    $remarks[$row['month']] = $row['remark'];
                }
                echo json_encode([
                    'status' => 'success',
                    'remarks' => $remarks
                ]);
            } catch(PDOException $e) {
                die(json_encode(['status'=>'error','message'=>'获取备注失败']));
            }
            break;
            
        case 'update_remark':
            // 更新备注（需要登录验证）
            $authUser = verifyWechatAuth();
            
            $data = $_POST;
            $required = ['year', 'month'];
            foreach ($required as $field) {
                if (!isset($data[$field])) {
                    die(json_encode(['status'=>'error','message'=>"缺少必填字段: $field"]));
                }
            }
            
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO shift_month_remarks (year, month, remark)
                    VALUES (?, ?, ?)
                    ON DUPLICATE KEY UPDATE remark = VALUES(remark), updated_at = NOW()
                ");
                $stmt->execute([
                    $data['year'], 
                    $data['month'],
                    $data['remark'] ?? ''
                ]);

                // 设置SESSION以便logShiftAction能获取到用户ID
                if (isset($authUser['username'])) {
                    $_SESSION['user'] = [
                        'id' => $authUser['id'] ?? 0,
                        'username' => $authUser['username'],
                        'real_name' => $authUser['real_name'] ?? $authUser['username']
                    ];
                    logShiftAction('更新备注', $data);
                    unset($_SESSION['user']);
                }

                echo json_encode(['status' => 'success', 'message' => '备注更新成功']);
            } catch(PDOException $e) {
                die(json_encode(['status'=>'error','message'=>'更新备注失败']));
            }
            break;
            
        case 'verify_token':
            // 验证Token
            $tokenData = verifyToken();
            $newExpiry = date('Y-m-d\TH:i:s', time() + 86400);
            echo json_encode([
                'status' => 'success',
                'new_expiry' => $newExpiry,
                'user' => $tokenData['username']
            ]);
            break;

        case 'logout':
            handleLogout();
            break;

        default:
            die(json_encode([
                'status' => 'error',
                'message' => '无效的操作',
                'available_actions' => [
                    'login', 'get_data', 'add_schedule', 'update_schedule', 
                    'delete_schedule', 'get_remarks', 'update_remark', 'verify_token'
                ]
            ]));
    }
    
} catch (Exception $e) {
    die(json_encode(['status'=>'error','message'=>'服务器内部错误']));
}

/**
 * 处理退出登录
 */
function handleLogout() {
    try {
        // 验证基本参数
        if (!isset($_POST['username'])) {
            echo json_encode(['status' => 'error', 'message' => '缺少用户名参数']);
            exit;
        }

        $username = $_POST['username'];

        // 查询用户信息以获取用户ID
        global $pdo;
        $stmt = $pdo->prepare("SELECT id, username FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if ($user) {
            // 记录操作日志
            if (function_exists('logAction')) {
                // 临时设置SESSION以便logAction能获取到用户ID
                $_SESSION['user'] = ['id' => $user['id'], 'username' => $user['username']];
                logAction('用户退出', 'logout', ['username' => $username]);
                // 清除临时SESSION
                unset($_SESSION['user']);
            }
        }

        echo json_encode(['status' => 'success', 'message' => '退出成功']);

    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '退出处理失败']);
    }
}
?>
