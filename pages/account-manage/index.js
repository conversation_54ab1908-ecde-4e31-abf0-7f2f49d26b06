const { loginManager } = require('../../utils/login.js');

Page({
  data: {
    userInfo: null,
    showChangePasswordModal: false,
    showChangeRealNameModal: false,
    passwordForm: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    },
    realNameForm: {
      newRealName: ''
    },
    loading: false
  },

  onLoad: function (options) {
    this.loadUserInfo();
  },

  onShow: function() {
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo: function() {
    const userInfo = loginManager.getUserInfo();
    if (userInfo) {
      this.setData({
        userInfo: {
          ...userInfo,
          formattedRole: this.formatUserRole(userInfo)
        }
      });
    } else {
      // 如果没有用户信息，返回上一页
      wx.navigateBack();
    }
  },

  // 格式化用户角色显示
  formatUserRole: function(userInfo) {
    if (!userInfo) return '';

    let roleText = '普通用户';
    if (userInfo.role === 'admin') {
      roleText = '管理员';
    } else if (userInfo.role === 'manager') {
      roleText = '普通管理员';
    }

    // 正确判断是否为一站人员，处理字符串和数字类型
    if (userInfo.is_station_staff === true || userInfo.is_station_staff === 1 || userInfo.is_station_staff === '1') {
      roleText += ' | 一站人员';
    }

    return roleText;
  },

  // 修改密码
  onChangePassword: function() {
    this.setData({
      showChangePasswordModal: true,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    });
  },

  // 修改真实姓名
  onChangeRealName: function() {
    this.setData({
      showChangeRealNameModal: true,
      realNameForm: {
        newRealName: this.data.userInfo.real_name || ''
      }
    });
  },

  // 解除微信绑定
  onUnbindWechat: function() {
    const self = this;
    wx.showModal({
      title: '确认解绑',
      content: '解绑后将切换为游客模式，无法使用微信快捷登录和部分功能，确定要解绑吗？',
      success: async function(res) {
        if (res.confirm) {
          self.setData({ loading: true });

          try {
            const result = await loginManager.unbindAccount();
            if (result) {
              // 解绑成功后返回到我的页面，让用户看到游客模式状态
              setTimeout(() => {
                wx.navigateBack();
              }, 2000);
            }
          } catch (error) {
            console.error('解绑失败:', error);
          } finally {
            self.setData({ loading: false });
          }
        }
      }
    });
  },

  // 查看登录记录
  onViewLoginHistory: function() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 关闭修改密码弹窗
  closePasswordModal: function() {
    this.setData({
      showChangePasswordModal: false
    });
  },

  // 关闭修改真实姓名弹窗
  closeRealNameModal: function() {
    this.setData({
      showChangeRealNameModal: false
    });
  },

  // 密码表单输入
  onOldPasswordInput: function(e) {
    this.setData({
      'passwordForm.oldPassword': e.detail.value
    });
  },

  onNewPasswordInput: function(e) {
    this.setData({
      'passwordForm.newPassword': e.detail.value
    });
  },

  onConfirmPasswordInput: function(e) {
    this.setData({
      'passwordForm.confirmPassword': e.detail.value
    });
  },

  // 真实姓名表单输入
  onRealNameInput: function(e) {
    this.setData({
      'realNameForm.newRealName': e.detail.value
    });
  },

  // 确认修改密码
  confirmChangePassword: async function() {
    const { oldPassword, newPassword, confirmPassword } = this.data.passwordForm;
    
    if (!oldPassword || !newPassword || !confirmPassword) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      wx.showToast({
        title: '两次密码不一致',
        icon: 'none'
      });
      return;
    }

    if (newPassword.length < 6) {
      wx.showToast({
        title: '密码至少6位',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      // 调用修改密码API
      const response = await loginManager.callAPI('change_password', {
        token: loginManager.token,
        old_password: oldPassword,
        new_password: newPassword
      });

      if (response.status === 'success') {
        wx.showToast({
          title: '密码修改成功',
          icon: 'success'
        });
        this.closePasswordModal();
      } else {
        throw new Error(response.message || '修改失败');
      }
    } catch (error) {
      wx.showToast({
        title: error.message || '修改失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 确认修改真实姓名
  confirmChangeRealName: async function() {
    const { newRealName } = this.data.realNameForm;

    if (!newRealName || newRealName.trim() === '') {
      wx.showToast({
        title: '请输入真实姓名',
        icon: 'none'
      });
      return;
    }

    if (newRealName.trim() === this.data.userInfo.real_name) {
      wx.showToast({
        title: '真实姓名未改变',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      // 调用修改真实姓名API
      const result = await loginManager.changeRealName(newRealName.trim());

      if (result.success) {
        // 更新本地用户信息
        const updatedUserInfo = { ...this.data.userInfo, real_name: newRealName.trim() };
        updatedUserInfo.formattedRole = this.formatUserRole(updatedUserInfo);
        this.setData({ userInfo: updatedUserInfo });

        this.closeRealNameModal();
      }
    } catch (error) {
      console.error('修改真实姓名失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止事件冒泡
  }
});
