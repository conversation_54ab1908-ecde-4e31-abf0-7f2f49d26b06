<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section" bindtap="onAvatarTap">
    <view class="user-avatar">
      <image src="{{userInfo.avatarUrl}}" class="avatar-image" mode="aspectFill"></image>
    </view>
    <view class="user-info">
      <text class="user-name">{{userInfo.nickName}}</text>
      <text class="user-status" wx:if="{{!isLoggedIn}}">点击登录</text>
      <text class="user-status" wx:else>{{userInfo.role || '已登录'}}</text>
    </view>
    <view class="arrow-icon">
      <text class="iconfont">›</text>
    </view>
  </view>

  <!-- 功能列表 -->
  <view class="function-list">
    <!-- 意见反馈 -->
    <view class="function-item" bindtap="onFeedback">
      <view class="function-icon">
        <text class="icon-emoji">💬</text>
      </view>
      <view class="function-info">
        <text class="function-title">意见反馈</text>
        <text class="function-desc">告诉我们您的建议</text>
      </view>
      <view class="arrow-icon">
        <text class="iconfont">›</text>
      </view>
    </view>

    <!-- 用户管理 - 仅管理员可见 -->
    <view class="function-item admin-only" wx:if="{{isAdmin}}" bindtap="onUserManagement">
      <view class="function-icon">
        <text class="icon-emoji">👥</text>
      </view>
      <view class="function-info">
        <text class="function-title">用户管理</text>
        <text class="function-desc">管理系统用户账号</text>
      </view>
      <view class="arrow-icon">
        <text class="iconfont">›</text>
      </view>
      <view class="admin-badge">
        <text>管理员</text>
      </view>
    </view>



    <!-- 退出登录 -->
    <view class="function-item" wx:if="{{isLoggedIn}}" bindtap="onLogout">
      <view class="function-icon">
        <text class="icon-emoji">🚪</text>
      </view>
      <view class="function-info">
        <text class="function-title">退出登录</text>
        <text class="function-desc">退出当前账户</text>
      </view>
      <view class="arrow-icon">
        <text class="iconfont">›</text>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="bottom-info">
    <view class="company-info">
      <text class="company-name">海上平台常用查询工具</text>
      <text class="company-english">Offshore Platform Common Query Tools</text>
    </view>
    <view class="contact-info">

      <text class="contact-item">E-mail：<EMAIL></text>
      <text class="contact-item">Https://wx.sunxiyue.com</text>
    </view>
    <view class="welcome-text">
      <text>欢迎您使用本工具，如您发现本工具的任何缺陷可向开发组进行反馈，如您有更好的建议，也欢迎您提出，如您便用本工具时未能满足您的需求，或您有其他工具使用的需求时，也欢迎您向我们提出。开发组联系方式：</text>
      <text class="contact-email"><EMAIL></text>
    </view>
  </view>

  <!-- 登录弹窗 -->
  <view class="login-modal" wx:if="{{showLoginModal}}">
    <view class="modal-mask" bindtap="closeLoginModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">登录</text>
        <view class="modal-close" bindtap="closeLoginModal">
          <text>×</text>
        </view>
      </view>
      <view class="modal-body">
        <view class="login-avatar">
          <image src="/images/zhuye/wd.png" class="login-avatar-image"></image>
        </view>
        <text class="login-desc">登录后可享受更多服务</text>
        <button class="login-btn" bindtap="onWechatLogin" loading="{{loginLoading}}">
          <text class="login-btn-text">微信登录</text>
        </button>
        <view class="login-tip">
          <text>点击登录后，请授权获取您的微信信息</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 账号绑定弹窗 -->
  <view class="bind-modal" wx:if="{{showBindModal}}">
    <view class="modal-mask" bindtap="closeBindModal"></view>
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">绑定系统账号</text>
        <view class="modal-close" bindtap="closeBindModal">
          <text>×</text>
        </view>
      </view>
      <view class="modal-body">
        <view class="bind-info">
          <text class="bind-text">绑定系统账号</text>
          <text class="bind-desc">绑定后可使用完整功能，连续错误5次将锁定1小时</text>
        </view>
        <view class="form-group">
          <text class="form-label">用户名</text>
          <input class="form-input" placeholder="请输入用户名" value="{{bindForm.username}}" bindinput="onBindUsernameInput" />
        </view>
        <view class="form-group">
          <text class="form-label">密码</text>
          <input class="form-input" type="password" password="true" placeholder="请输入密码" value="{{bindForm.password}}" bindinput="onBindPasswordInput" />
        </view>
        <view class="skip-bind-info">
          <text class="skip-text">跳过绑定将限制部分功能使用</text>
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn skip" bindtap="skipBind">跳过绑定</button>
        <button class="modal-btn confirm" bindtap="confirmBind" loading="{{bindLoading}}">立即绑定</button>
      </view>
    </view>
  </view>
</view>
