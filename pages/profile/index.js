const { loginManager } = require('../../utils/login.js');

Page({
  data: {
    userInfo: {
      avatarUrl: '/images/zhuye/wd.png', // 默认头像
      nickName: '匿名用户'
    },
    isLoggedIn: false,
    isAdmin: false, // 是否为管理员
    showLoginModal: false,
    showBindModal: false,
    loginLoading: false,
    bindLoading: false,
    bindData: null,
    bindForm: {
      username: '',
      password: ''
    },
    isGuestMode: false // 游客模式标识
  },

  onLoad: function (options) {
    // 检查是否已登录
    this.checkLoginStatus();
  },

  onShow: function() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  // 检查登录状态
  async checkLoginStatus() {
    const isLoggedIn = loginManager.isLoggedIn();
    const isGuestMode = wx.getStorageSync('guest_mode') || false;

    if (isLoggedIn) {
      // 验证token有效性
      const isValid = await loginManager.verifyToken();
      if (isValid) {
        const userInfo = loginManager.getUserInfo();
        const isAdmin = userInfo && userInfo.role === 'admin';
        this.setData({
          isLoggedIn: true,
          isGuestMode: false,
          isAdmin: isAdmin,
          userInfo: {
            avatarUrl: userInfo.avatar || '/images/zhuye/wd.png',
            nickName: userInfo.real_name || userInfo.username || '系统用户',
            role: this.formatUserRole(userInfo)
          }
        });
      } else {
        this.setData({
          isLoggedIn: false,
          isGuestMode: false,
          isAdmin: false,
          userInfo: {
            avatarUrl: '/images/zhuye/wd.png',
            nickName: '匿名用户'
          }
        });
      }
    } else if (isGuestMode) {
      // 游客模式
      this.setData({
        isLoggedIn: true,
        isGuestMode: true,
        isAdmin: false,
        userInfo: {
          avatarUrl: '/images/zhuye/wd.png',
          nickName: '游客用户',
          role: '游客模式 (功能受限)'
        }
      });
    } else {
      this.setData({
        isLoggedIn: false,
        isGuestMode: false,
        isAdmin: false,
        userInfo: {
          avatarUrl: '/images/zhuye/wd.png',
          nickName: '匿名用户'
        }
      });
    }
  },

  // 格式化用户角色显示
  formatUserRole(userInfo) {
    if (!userInfo) return '';

    let roleText = '普通用户';
    if (userInfo.role === 'admin') {
      roleText = '管理员';
    } else if (userInfo.role === 'manager') {
      roleText = '普通管理员';
    }

    // 正确判断是否为一站人员，处理字符串和数字类型
    if (userInfo.is_station_staff === true || userInfo.is_station_staff === 1 || userInfo.is_station_staff === '1') {
      roleText += ' | 一站人员';
    }

    return roleText;
  },

  // 点击头像区域
  onAvatarTap: function() {
    if (!this.data.isLoggedIn) {
      this.setData({
        showLoginModal: true
      });
    } else if (this.data.isGuestMode) {
      // 游客模式，提示绑定账号
      wx.showModal({
        title: '游客模式',
        content: '您当前处于游客模式，绑定系统账号后可使用完整功能。是否立即绑定？',
        confirmText: '立即绑定',
        cancelText: '继续游客',
        success: async (res) => {
          if (res.confirm) {
            let openid = wx.getStorageSync('wechat_openid');
            let wechatUserId = wx.getStorageSync('wechat_user_id') || '1';

            // 如果没有openid，尝试重新获取
            if (!openid) {
              try {
                const loginRes = await loginManager.wxLogin();
                const response = await loginManager.callAPI('wechat_login', {
                  code: loginRes.code,
                  userInfo: null
                });

                if (response.status === 'need_bind' && response.data.openid) {
                  openid = response.data.openid;
                  wechatUserId = response.data.wechat_user_id;
                  wx.setStorageSync('wechat_openid', openid);
                  wx.setStorageSync('wechat_user_id', wechatUserId);
                }
              } catch (error) {
                console.error('获取openid失败:', error);
                wx.showToast({
                  title: '获取微信信息失败，请重试',
                  icon: 'none'
                });
                return;
              }
            }

            if (!openid) {
              wx.showToast({
                title: '无法获取微信信息，请重新登录',
                icon: 'none'
              });
              return;
            }

            this.setData({
              showBindModal: true,
              bindData: {
                openid: openid,
                wechat_user_id: wechatUserId
              }
            });
          }
        }
      });
    } else {
      // 已绑定用户进入个人管理页面
      wx.navigateTo({
        url: '/pages/account-manage/index'
      });
    }
  },

  // 关闭登录弹窗
  closeLoginModal: function() {
    this.setData({
      showLoginModal: false
    });
  },

  // 关闭绑定弹窗
  closeBindModal: function() {
    this.setData({
      showBindModal: false,
      bindForm: {
        username: '',
        password: ''
      }
    });
  },

  // 微信登录
  async onWechatLogin() {
    this.setData({ loginLoading: true });

    try {
      // 直接尝试获取用户信息，因为这是在用户点击事件中调用的
      let result = await loginManager.wechatLogin(true);

      if (result.success) {
        // 登录成功
        const userInfo = result.data.user;
        if (userInfo) {
          this.setData({
            isLoggedIn: true,
            userInfo: {
              avatarUrl: userInfo.avatar || '/images/zhuye/wd.png',
              nickName: userInfo.username || '系统用户',
              role: this.formatUserRole(userInfo)
            },
            showLoginModal: false
          });
        } else {
          console.error('登录成功但用户信息为空:', result);
          wx.showToast({
            title: '登录数据异常',
            icon: 'none'
          });
        }
      } else if (result.needBind) {
        // 需要绑定账号
        this.setData({
          showLoginModal: false,
          showBindModal: true,
          bindData: result.data
        });
      }
    } catch (error) {
      console.error('微信登录失败:', error);
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  // 确认绑定账号
  async confirmBind() {
    const { username, password } = this.data.bindForm;
    const { bindData } = this.data;

    if (!username || !password) {
      wx.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      });
      return;
    }

    this.setData({ bindLoading: true });

    try {
      const result = await loginManager.bindAccount(bindData, username, password);

      if (result.success) {
        // 绑定成功，从游客模式升级为正式用户
        const userInfo = result.data.user;

        // 清除游客模式标识
        wx.removeStorageSync('guest_mode');
        wx.removeStorageSync('wechat_openid');

        this.setData({
          isLoggedIn: true,
          isGuestMode: false,
          userInfo: {
            avatarUrl: userInfo.avatar || '/images/zhuye/wd.png',
            nickName: userInfo.real_name || userInfo.username || '系统用户',
            role: this.formatUserRole(userInfo)
          },
          showBindModal: false,
          bindForm: { username: '', password: '' }
        });

        wx.showToast({
          title: '绑定成功，已升级为正式用户',
          icon: 'success',
          duration: 2000
        });
      } else {
        // 绑定失败，根据错误类型进行不同处理
        const errorType = result.type || 'general';

        if (errorType === 'ip_locked') {
          // IP锁定错误，关闭绑定弹窗，错误信息已在 loginManager 中显示
          this.setData({
            showBindModal: false,
            bindForm: { username: '', password: '' }
          });
        } else if (errorType === 'auth_error') {
          // 用户名密码错误，清空密码字段但保留用户名，错误信息已在 loginManager 中显示
          this.setData({
            'bindForm.password': ''
          });
        } else if (errorType === 'conflict') {
          // 绑定冲突，等待用户选择，不做额外处理
        } else {
          // 其他一般错误，显示错误信息
          console.error('绑定失败:', result.error);
        }
      }
    } catch (error) {
      console.error('绑定异常:', error);
      wx.showToast({
        title: '绑定过程中发生异常，请重试',
        icon: 'none',
        duration: 3000
      });
    } finally {
      this.setData({ bindLoading: false });
    }
  },

  // 跳过绑定，进入游客模式
  skipBind: function() {
    const self = this;
    wx.showModal({
      title: '确认跳过绑定',
      content: '跳过绑定后将以游客身份使用，部分功能将受限。您可以随时在个人中心进行绑定。',
      confirmText: '确认跳过',
      cancelText: '继续绑定',
      success: function(res) {
        if (res.confirm) {
          // 进入游客模式
          self.setData({
            isLoggedIn: true,
            isGuestMode: true,
            userInfo: {
              avatarUrl: '/images/zhuye/wd.png',
              nickName: '游客用户',
              role: '游客模式 (功能受限)'
            },
            showBindModal: false
          });

          // 保存游客模式标识和微信信息
          wx.setStorageSync('guest_mode', true);
          wx.setStorageSync('wechat_openid', self.data.bindData.openid);

          wx.showToast({
            title: '已进入游客模式',
            icon: 'success'
          });
        }
      }
    });
  },

  // 退出登录
  onLogout: function() {
    const self = this;
    const logoutText = this.data.isGuestMode ? '退出游客模式' : '退出登录';
    const contentText = this.data.isGuestMode ? '确定要退出游客模式吗？' : '确定要退出登录吗？';

    wx.showModal({
      title: '提示',
      content: contentText,
      success: async function(res) {
        if (res.confirm) {
          if (self.data.isGuestMode) {
            // 退出游客模式
            wx.removeStorageSync('guest_mode');
            wx.removeStorageSync('wechat_openid');
          } else {
            // 使用新的正式退出登录功能
            await loginManager.logout();
          }

          self.setData({
            userInfo: {
              avatarUrl: '/images/zhuye/wd.png',
              nickName: '匿名用户'
            },
            isLoggedIn: false,
            isGuestMode: false
          });

          wx.showToast({
            title: logoutText + '成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 意见反馈
  onFeedback: function() {
    wx.navigateTo({
      url: '/pages/feedback/index'
    });
  },

  // 用户管理 - 仅管理员可访问
  onUserManagement: function() {
    // 双重检查管理员权限
    const userInfo = wx.getStorageSync('user_info');
    const token = wx.getStorageSync('wechat_token');

    if (!token || !userInfo || userInfo.role !== 'admin') {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以访问用户管理功能',
        showCancel: false
      });
      return;
    }

    // 跳转到用户管理页面
    wx.navigateTo({
      url: '/pages/user-management/index',
      fail: (error) => {
        console.error('跳转用户管理页面失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },



  // 表单输入处理
  onBindUsernameInput: function(e) {
    this.setData({
      'bindForm.username': e.detail.value
    });
  },

  onBindPasswordInput: function(e) {
    this.setData({
      'bindForm.password': e.detail.value
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止事件冒泡
  }
});
