// 免责声明页面
Page({
  data: {

  },

  onLoad(options) {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '免责声明'
    });
  },

  // 返回首页
  backToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用查询工具 - 免责声明',
      path: '/pages/index/disclaimer?from=share',
      imageUrl: ''
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '平台常用查询工具 - 免责声明',
      query: 'from=timeline',
      imageUrl: ''
    };
  }
});