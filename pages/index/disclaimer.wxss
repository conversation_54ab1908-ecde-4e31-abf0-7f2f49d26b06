/* 免责声明页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

/* 页面头部 */
.header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  padding: 60rpx 40rpx 40rpx 40rpx;
  text-align: center;
  border-bottom: 1rpx solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4rpx 16rpx rgba(71, 85, 105, 0.06);
}

.title {
  font-size: 44rpx;
  font-weight: 700;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.subtitle {
  font-size: 26rpx;
  color: rgba(100, 116, 139, 0.8);
  font-weight: 400;
}

/* 内容区域 */
.content {
  padding: 40rpx;
  padding-bottom: 160rpx; /* 为底部按钮留出空间 */
}

.section {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15rpx);
  -webkit-backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  padding: 32rpx;
  box-shadow:
    0 4rpx 16rpx rgba(71, 85, 105, 0.06),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  line-height: 1.4;
}

.section-content {
  padding-left: 8rpx;
}

.item {
  font-size: 28rpx;
  line-height: 1.6;
  color: rgba(51, 65, 85, 0.8);
  margin-bottom: 16rpx;
  padding-left: 8rpx;
}

.item:last-child {
  margin-bottom: 0;
}

/* 底部区域 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border-top: 1rpx solid rgba(226, 232, 240, 0.6);
  padding: 32rpx 40rpx;
  text-align: center;
  box-shadow: 0 -4rpx 16rpx rgba(71, 85, 105, 0.06);
}

.back-btn {
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16rpx auto;
  min-width: 240rpx;
  box-shadow:
    0 8rpx 24rpx rgba(0, 122, 255, 0.3),
    0 2rpx 8rpx rgba(0, 122, 255, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.back-btn:active {
  transform: scale(0.98);
  box-shadow:
    0 4rpx 16rpx rgba(0, 122, 255, 0.2),
    0 1rpx 4rpx rgba(0, 122, 255, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.back-btn::after {
  border: none;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.btn-text {
  font-size: 30rpx;
}

.update-time {
  font-size: 22rpx;
  color: rgba(100, 116, 139, 0.6);
  text-align: center;
}