<!-- 免责声明页面 -->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="title">免责声明</view>
    <view class="subtitle">平台常用查询工具使用须知</view>
  </view>

  <!-- 声明内容 -->
  <view class="content">
    <!-- 使用说明 -->
    <view class="section">
      <view class="section-title">📋 使用说明</view>
      <view class="section-content">
        <view class="item">• 本工具提供的所有查询结果和计算数据仅供参考学习使用</view>
        <view class="item">• 不得将查询结果作为工程设计、施工或检测的唯一依据</view>
        <view class="item">• 实际应用中请务必参考相关国家标准和行业规范</view>
      </view>
    </view>

    <!-- 数据准确性 -->
    <view class="section">
      <view class="section-title">⚠️ 数据准确性</view>
      <view class="section-content">
        <view class="item">• 查询数据来源于公开资料和标准文档，力求准确但不保证完全无误</view>
        <view class="item">• 用户输入错误或数据理解偏差可能导致结果不准确</view>
        <view class="item">• 建议重要应用场景下通过多种渠道验证数据准确性</view>
      </view>
    </view>

    <!-- 责任限制 -->
    <view class="section">
      <view class="section-title">🛡️ 责任限制</view>
      <view class="section-content">
        <view class="item">• 开发者不承担因使用本工具而产生的任何直接或间接损失</view>
        <view class="item">• 用户应根据实际情况谨慎使用查询结果</view>
        <view class="item">• 涉及安全的应用场景请咨询专业技术人员</view>
      </view>
    </view>

    <!-- 专业建议 -->
    <view class="section">
      <view class="section-title">👨‍🔧 专业建议</view>
      <view class="section-content">
        <view class="item">• 工程设计请遵循相关设计规范和标准</view>
        <view class="item">• 设备选型和安装请咨询专业工程师</view>
        <view class="item">• 安全相关应用请通过专业检测机构验证</view>
      </view>
    </view>

    <!-- 更新说明 -->
    <view class="section">
      <view class="section-title">🔄 更新说明</view>
      <view class="section-content">
        <view class="item">• 我们会持续更新和完善工具功能</view>
        <view class="item">• 标准规范变更时会及时更新相关数据</view>
        <view class="item">• 如发现数据错误，欢迎通过意见反馈告知我们</view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="footer">
    <button class="back-btn" bindtap="backToHome">
      <text class="btn-icon">🏠</text>
      <text class="btn-text">返回首页</text>
    </button>
    <view class="update-time">最后更新：2024年8月</view>
  </view>
</view>