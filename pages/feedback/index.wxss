/* 页面背景样式已移除，使用容器样式代替 */

.container {
  padding: 0;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  -webkit-tap-highlight-color: transparent;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  z-index: 0;
}

/* 反馈表单 */
.feedback-form {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 28rpx;
  margin: 32rpx;
  padding: 40rpx;
  position: relative;
  z-index: 1;
  box-shadow:
    0 8rpx 24rpx rgba(71, 85, 105, 0.08),
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(226, 232, 240, 0.3);
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 16rpx;
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 选择器样式 */
.picker-content {
  width: 100%;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15rpx);
  -webkit-backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  border-radius: 16rpx;
  padding: 28rpx 32rpx;
  min-height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  transition: background 0.2s ease, transform 0.2s ease;
}

.picker-content:active {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(0.99);
}

.picker-text {
  font-size: 30rpx;
  color: rgba(30, 41, 59, 0.9);
  line-height: 1.4;
}

.picker-arrow {
  font-size: 36rpx;
  color: rgba(100, 116, 139, 0.6);
  font-weight: 300;
}

/* 输入框样式 */
.feedback-input {
  width: 100%;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15rpx);
  -webkit-backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  border-radius: 16rpx;
  padding: 28rpx 32rpx;
  min-height: 88rpx;
  font-size: 30rpx;
  line-height: 1.4;
  color: rgba(30, 41, 59, 0.9);
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.feedback-input:focus {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    0 0 0 4rpx rgba(59, 130, 246, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

/* 文本域样式 */
.feedback-textarea {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15rpx);
  -webkit-backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  font-size: 30rpx;
  line-height: 1.5;
  color: rgba(30, 41, 59, 0.9);
  min-height: 200rpx;
  width: 100%;
  box-sizing: border-box;
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.feedback-textarea:focus {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    0 0 0 4rpx rgba(59, 130, 246, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: rgba(100, 116, 139, 0.6);
  margin-top: 8rpx;
  line-height: 1.4;
  transition: color 0.2s ease;
}

.char-count-warning {
  color: #ff6b35 !important;
  font-weight: 500;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 1.2;
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  box-shadow:
    0 8rpx 24rpx rgba(0, 122, 255, 0.3),
    0 2rpx 8rpx rgba(0, 122, 255, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-top: 40rpx;
}

.submit-btn:active {
  transform: scale(0.98);
  box-shadow:
    0 4rpx 16rpx rgba(0, 122, 255, 0.2),
    0 1rpx 4rpx rgba(0, 122, 255, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

/* 温馨提示 */
.tips-section {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  border-radius: 24rpx;
  margin: 0 32rpx 32rpx 32rpx;
  padding: 32rpx;
  position: relative;
  z-index: 1;
  box-shadow:
    0 4rpx 16rpx rgba(71, 85, 105, 0.06),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
  margin-bottom: 20rpx;
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.tips-content {
  display: flex;
  flex-direction: column;
}

.tip-item {
  font-size: 26rpx;
  color: rgba(100, 116, 139, 0.8);
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 敏感词检测相关样式 */
.warning-text {
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 8rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

.warning-text::before {
  content: '⚠️';
  margin-right: 8rpx;
  font-size: 20rpx;
}

/* 输入提示样式 */
.input-hint {
  font-size: 22rpx;
  color: rgba(100, 116, 139, 0.5);
  margin-top: 8rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

.input-hint::before {
  content: '💡';
  margin-right: 8rpx;
  font-size: 18rpx;
}

/* 错误状态的输入框样式 */
.textarea-error {
  border-color: rgba(255, 71, 87, 0.5) !important;
  box-shadow:
    0 2rpx 8rpx rgba(255, 71, 87, 0.1),
    0 0 0 4rpx rgba(255, 71, 87, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9) !important;
}

.input-error {
  border-color: rgba(255, 71, 87, 0.5) !important;
  box-shadow:
    0 2rpx 8rpx rgba(255, 71, 87, 0.1),
    0 0 0 4rpx rgba(255, 71, 87, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9) !important;
}

/* 禁用状态的按钮样式 */
.btn-disabled {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%) !important;
  box-shadow:
    0 4rpx 16rpx rgba(148, 163, 184, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3) !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

.btn-disabled:active {
  transform: none !important;
}
