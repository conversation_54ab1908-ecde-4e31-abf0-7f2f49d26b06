// 引入敏感词过滤工具
const { sensitiveWordFilter } = require('../../utils/sensitiveWordFilter');

Page({
  data: {
    feedbackContent: '',
    contactInfo: '',
    feedbackType: '建议',
    typeOptions: ['建议', '问题反馈', '功能需求', '其他'],
    // 敏感词检测相关
    contentWarning: '',
    contactWarning: '',
    isContentValid: true,
    isContactValid: true
  },

  onLoad: function (options) {
    // 预加载敏感词列表
    this.preloadSensitiveWords();
  },

  // 预加载敏感词列表
  preloadSensitiveWords: function() {
    sensitiveWordFilter.loadSensitiveWords().catch(error => {
      console.warn('预加载敏感词列表失败:', error);
    });
  },

  // 选择反馈类型
  onTypeChange: function(e) {
    this.setData({
      feedbackType: this.data.typeOptions[e.detail.value]
    });
  },

  // 输入反馈内容
  onContentInput: function(e) {
    const content = e.detail.value;
    this.setData({
      feedbackContent: content
    });

    // 实时检测敏感词
    this.checkContentSensitiveWords(content);
  },

  // 检测反馈内容中的敏感词
  checkContentSensitiveWords: function(content) {
    if (!content.trim()) {
      this.setData({
        contentWarning: '',
        isContentValid: true
      });
      return;
    }

    sensitiveWordFilter.quickCheck(content).then(result => {
      this.setData({
        contentWarning: result.isValid ? '' : result.message,
        isContentValid: result.isValid
      });
    }).catch(error => {
      console.warn('敏感词检测失败:', error);
    });
  },

  // 输入联系方式
  onContactInput: function(e) {
    const contact = e.detail.value;

    // 过滤非法字符，只允许邮箱和电话号码相关字符
    const filteredContact = this.filterContactInput(contact);

    this.setData({
      contactInfo: filteredContact
    });

    // 验证联系方式格式和敏感词
    this.validateContactInfo(filteredContact);
  },

  // 过滤联系方式输入，只保留邮箱和电话号码相关字符
  filterContactInput: function(input) {
    // 只允许数字、字母、@、.、-、+、空格
    return input.replace(/[^\w@.\-+\s]/g, '');
  },

  // 验证联系方式格式和敏感词
  validateContactInfo: function(contact) {
    if (!contact.trim()) {
      this.setData({
        contactWarning: '',
        isContactValid: true
      });
      return;
    }

    const trimmedContact = contact.trim();

    // 长度检查
    if (trimmedContact.length > 30) {
      this.setData({
        contactWarning: '联系方式不能超过30个字符',
        isContactValid: false
      });
      return;
    }

    if (trimmedContact.length < 6) {
      this.setData({
        contactWarning: '联系方式至少需要6个字符',
        isContactValid: false
      });
      return;
    }

    // 检查格式是否有效（邮箱或电话号码）
    const isValidFormat = this.isValidContactFormat(trimmedContact);

    if (!isValidFormat) {
      // 提供更具体的错误提示
      let errorMessage = '请输入有效的联系方式';
      if (trimmedContact.includes('@')) {
        errorMessage = '邮箱格式不正确，如：<EMAIL>';
      } else if (/^\d/.test(trimmedContact)) {
        errorMessage = '电话号码格式不正确，如：13800138000';
      }

      this.setData({
        contactWarning: errorMessage,
        isContactValid: false
      });
      return;
    }

    // 检测敏感词
    sensitiveWordFilter.quickCheck(contact).then(result => {
      this.setData({
        contactWarning: result.isValid ? '' : result.message,
        isContactValid: result.isValid && isValidFormat
      });
    }).catch(error => {
      console.warn('敏感词检测失败:', error);
      this.setData({
        contactWarning: '',
        isContactValid: isValidFormat
      });
    });
  },

  // 检查联系方式格式是否有效
  isValidContactFormat: function(contact) {
    // 长度检查
    if (contact.length > 30) {
      return false;
    }

    // 邮箱格式验证（长度限制在6-30字符）
    const emailRegex = /^[a-zA-Z0-9._%+-]{1,20}@[a-zA-Z0-9.-]{1,15}\.[a-zA-Z]{2,4}$/;

    // 电话号码格式验证（长度限制在7-20字符）
    const phoneRegex = /^[\+]?[\d\s\-\(\)]{7,20}$/;

    // 检查邮箱格式
    if (emailRegex.test(contact)) {
      return contact.length >= 6 && contact.length <= 30;
    }

    // 检查电话格式
    if (phoneRegex.test(contact)) {
      // 去除格式字符后检查数字长度
      const numbersOnly = contact.replace(/[\s\-\(\)+]/g, '');
      return numbersOnly.length >= 7 && numbersOnly.length <= 15;
    }

    return false;
  },

  // 提交反馈
  onSubmit: function() {
    if (!this.data.feedbackContent.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }

    if (this.data.feedbackContent.trim().length > 500) {
      wx.showToast({
        title: '反馈内容不能超过500字',
        icon: 'none'
      });
      return;
    }

    // 前端敏感词检测
    if (!this.data.isContentValid) {
      wx.showToast({
        title: '反馈内容包含不当词汇，请修改后提交',
        icon: 'none',
        duration: 3000
      });
      return;
    }

    if (!this.data.isContactValid) {
      wx.showToast({
        title: '联系方式包含不当词汇，请修改后提交',
        icon: 'none',
        duration: 3000
      });
      return;
    }

    wx.showLoading({
      title: '提交中...'
    });

    // 获取用户信息
    const userInfo = wx.getStorageSync('user_info');
    const wechatToken = wx.getStorageSync('wechat_token');

    // 提交反馈
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/feedback_api.php',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        action: 'submit_feedback',
        feedback_type: this.data.feedbackType,
        content: this.data.feedbackContent.trim(),
        contact_info: this.data.contactInfo.trim(),
        wechat_auth: !!wechatToken,
        user_info: userInfo || null
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data && res.data.success) {
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          });

          // 清空表单
          this.setData({
            feedbackContent: '',
            contactInfo: '',
            feedbackType: '建议',
            contentWarning: '',
            contactWarning: '',
            isContentValid: true,
            isContactValid: true
          });
        } else {
          wx.showToast({
            title: res.data?.message || '提交失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('提交反馈失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '意见反馈 - 平台常用查询工具',
      path: '/pages/feedback/index?from=share',
      desc: '欢迎使用意见反馈功能，您的建议是我们改进的动力',
      imageUrl: '' // 可以设置自定义分享图片
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '意见反馈 - 平台常用查询工具',
      query: 'from=timeline',
      imageUrl: '' // 可以设置自定义分享图片
    };
  }
});
