Page({
  data: {
    feedbackContent: '',
    contactInfo: '',
    feedbackType: '建议',
    typeOptions: ['建议', '问题反馈', '功能需求', '其他']
  },

  onLoad: function (options) {
    
  },

  // 选择反馈类型
  onTypeChange: function(e) {
    this.setData({
      feedbackType: this.data.typeOptions[e.detail.value]
    });
  },

  // 输入反馈内容
  onContentInput: function(e) {
    this.setData({
      feedbackContent: e.detail.value
    });
  },

  // 输入联系方式
  onContactInput: function(e) {
    this.setData({
      contactInfo: e.detail.value
    });
  },

  // 提交反馈
  onSubmit: function() {
    if (!this.data.feedbackContent.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }

    if (this.data.feedbackContent.trim().length > 500) {
      wx.showToast({
        title: '反馈内容不能超过500字',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '提交中...'
    });

    // 获取用户信息
    const userInfo = wx.getStorageSync('user_info');
    const wechatToken = wx.getStorageSync('wechat_token');

    // 提交反馈
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/feedback_api.php',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        action: 'submit_feedback',
        feedback_type: this.data.feedbackType,
        content: this.data.feedbackContent.trim(),
        contact_info: this.data.contactInfo.trim(),
        wechat_auth: !!wechatToken,
        user_info: userInfo || null
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data && res.data.success) {
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          });

          // 清空表单
          this.setData({
            feedbackContent: '',
            contactInfo: '',
            feedbackType: '建议'
          });
        } else {
          wx.showToast({
            title: res.data?.message || '提交失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('提交反馈失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  }
});
