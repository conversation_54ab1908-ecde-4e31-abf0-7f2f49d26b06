// 引入敏感词过滤工具
const { sensitiveWordFilter } = require('../../utils/sensitiveWordFilter');

Page({
  data: {
    feedbackContent: '',
    contactInfo: '',
    feedbackType: '建议',
    typeOptions: ['建议', '问题反馈', '功能需求', '其他'],
    // 敏感词检测相关
    contentWarning: '',
    contactWarning: '',
    isContentValid: true,
    isContactValid: true
  },

  onLoad: function (options) {
    // 预加载敏感词列表
    this.preloadSensitiveWords();
  },

  // 预加载敏感词列表
  preloadSensitiveWords: function() {
    sensitiveWordFilter.loadSensitiveWords().catch(error => {
      console.warn('预加载敏感词列表失败:', error);
    });
  },

  // 选择反馈类型
  onTypeChange: function(e) {
    this.setData({
      feedbackType: this.data.typeOptions[e.detail.value]
    });
  },

  // 输入反馈内容
  onContentInput: function(e) {
    const content = e.detail.value;
    this.setData({
      feedbackContent: content
    });

    // 实时检测敏感词
    this.checkContentSensitiveWords(content);
  },

  // 检测反馈内容中的敏感词
  checkContentSensitiveWords: function(content) {
    if (!content.trim()) {
      this.setData({
        contentWarning: '',
        isContentValid: true
      });
      return;
    }

    sensitiveWordFilter.quickCheck(content).then(result => {
      this.setData({
        contentWarning: result.isValid ? '' : result.message,
        isContentValid: result.isValid
      });
    }).catch(error => {
      console.warn('敏感词检测失败:', error);
    });
  },

  // 输入联系方式
  onContactInput: function(e) {
    const contact = e.detail.value;
    this.setData({
      contactInfo: contact
    });

    // 实时检测敏感词
    this.checkContactSensitiveWords(contact);
  },

  // 检测联系方式中的敏感词
  checkContactSensitiveWords: function(contact) {
    if (!contact.trim()) {
      this.setData({
        contactWarning: '',
        isContactValid: true
      });
      return;
    }

    sensitiveWordFilter.quickCheck(contact).then(result => {
      this.setData({
        contactWarning: result.isValid ? '' : result.message,
        isContactValid: result.isValid
      });
    }).catch(error => {
      console.warn('敏感词检测失败:', error);
    });
  },

  // 提交反馈
  onSubmit: function() {
    if (!this.data.feedbackContent.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }

    if (this.data.feedbackContent.trim().length > 500) {
      wx.showToast({
        title: '反馈内容不能超过500字',
        icon: 'none'
      });
      return;
    }

    // 前端敏感词检测
    if (!this.data.isContentValid) {
      wx.showToast({
        title: '反馈内容包含不当词汇，请修改后提交',
        icon: 'none',
        duration: 3000
      });
      return;
    }

    if (!this.data.isContactValid) {
      wx.showToast({
        title: '联系方式包含不当词汇，请修改后提交',
        icon: 'none',
        duration: 3000
      });
      return;
    }

    wx.showLoading({
      title: '提交中...'
    });

    // 获取用户信息
    const userInfo = wx.getStorageSync('user_info');
    const wechatToken = wx.getStorageSync('wechat_token');

    // 提交反馈
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/feedback_api.php',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        action: 'submit_feedback',
        feedback_type: this.data.feedbackType,
        content: this.data.feedbackContent.trim(),
        contact_info: this.data.contactInfo.trim(),
        wechat_auth: !!wechatToken,
        user_info: userInfo || null
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data && res.data.success) {
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          });

          // 清空表单
          this.setData({
            feedbackContent: '',
            contactInfo: '',
            feedbackType: '建议',
            contentWarning: '',
            contactWarning: '',
            isContentValid: true,
            isContactValid: true
          });
        } else {
          wx.showToast({
            title: res.data?.message || '提交失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('提交反馈失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  }
});
