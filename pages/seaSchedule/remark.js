// pages/seaSchedule/remark.js
Page({
  data: {
    year: '',
    month: '',
    remarkContent: ''
  },

  onLoad(options) {
    // 检查管理员权限
    const userInfo = wx.getStorageSync('user_info');
    const token = wx.getStorageSync('wechat_token');

    if (!token || !userInfo || userInfo.role !== 'admin') {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以编辑备注',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.setData({
      year: options.year,
      month: options.month
    });
    this.loadRemark();
  },

  // 加载现有备注
  loadRemark() {
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
      data: {
        action: 'get_remarks',
        year: this.data.year
      },
      success: (res) => {
        if (res.data.status === 'success') {
          this.setData({
            remarkContent: res.data.remarks[this.data.month] || ''
          });
        }
      }
    });
  },

  submitForm(e) {
    const remark = e.detail.value.remark || '';
    const userInfo = wx.getStorageSync('user_info');

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        action: 'update_remark',
        year: this.data.year,
        month: this.data.month,
        remark: remark,
        wechat_auth: true,
        user_info: userInfo
      },
      success: () => {
        wx.navigateBack();
      }
    });
  }
})