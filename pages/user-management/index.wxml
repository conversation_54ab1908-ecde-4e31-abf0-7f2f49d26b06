<!-- 用户管理列表页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">用户管理</text>
    <button class="add-btn" bindtap="navigateToAdd">
      <text class="add-icon">+</text>
      <text>添加用户</text>
    </button>
  </view>

  <!-- 搜索和筛选区域 -->
  <view class="search-section">
    <view class="search-box">
      <input 
        class="search-input" 
        placeholder="搜索用户名或真实姓名" 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <button class="search-btn" bindtap="onSearch">搜索</button>
    </view>
    
    <view class="filter-box">
      <picker 
        class="role-picker" 
        bindchange="onRoleChange" 
        value="{{roleIndex}}" 
        range="{{roleOptions}}"
        range-key="text"
      >
        <view class="picker-display">
          <text>{{roleOptions[roleIndex].text}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 用户列表 -->
  <view class="user-list" wx:if="{{!loading && userList.length > 0}}">
    <view class="user-item" wx:for="{{userList}}" wx:key="id">
      <view class="user-info">
        <view class="user-main">
          <text class="username">{{item.username}}</text>
          <text class="real-name" wx:if="{{item.real_name && item.real_name !== 'null'}}">{{item.real_name}}</text>
          <text class="real-name placeholder" wx:else>未设置真实姓名</text>
        </view>
        <view class="user-meta">
          <text class="role-tag role-{{item.role}}">{{item.role_text}}</text>
          <text class="station-tag" wx:if="{{item.is_station_staff == 1}}">一站人员</text>
          <text class="wechat-tag" wx:if="{{item.has_wechat}}">已绑定微信</text>
        </view>
      </view>

      <view class="user-actions">
        <button
          class="action-btn more-btn"
          bindtap="showActionSheet"
          data-user="{{item}}"
          data-id="{{item.id}}"
          data-username="{{item.username}}"
        >
          ⋯
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && userList.length === 0}}">
    <text class="empty-icon">👥</text>
    <text class="empty-text">暂无用户数据</text>
    <button class="empty-action" bindtap="navigateToAdd">添加第一个用户</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 分页信息 -->
  <view class="pagination" wx:if="{{pagination.total_pages > 1}}">
    <button 
      class="page-btn" 
      bindtap="prevPage" 
      disabled="{{pagination.current_page <= 1}}"
    >
      上一页
    </button>
    <text class="page-info">
      {{pagination.current_page}} / {{pagination.total_pages}}
    </text>
    <button 
      class="page-btn" 
      bindtap="nextPage" 
      disabled="{{pagination.current_page >= pagination.total_pages}}"
    >
      下一页
    </button>
  </view>

  <!-- 统计信息 -->
  <view class="stats-info" wx:if="{{pagination.total_items > 0}}">
    <text>共 {{pagination.total_items}} 个用户</text>
  </view>
</view>
