/**
 * 前端敏感词过滤工具
 * 提供即时的用户体验反馈，配合后端API进行双重验证
 */

class SensitiveWordFilter {
  constructor() {
    this.sensitiveWords = [];
    this.isLoaded = false;
    this.loadPromise = null;
  }

  /**
   * 获取敏感词列表（从后端API获取最新的敏感词库）
   */
  async loadSensitiveWords() {
    if (this.loadPromise) {
      return this.loadPromise;
    }

    this.loadPromise = new Promise((resolve, reject) => {
      // 先尝试从缓存获取
      const cachedWords = wx.getStorageSync('sensitive_words_cache');
      const cacheTime = wx.getStorageSync('sensitive_words_cache_time');
      const now = Date.now();
      
      // 如果缓存存在且未过期（缓存1小时）
      if (cachedWords && cacheTime && (now - cacheTime < 60 * 60 * 1000)) {
        this.sensitiveWords = cachedWords;
        this.isLoaded = true;
        resolve(this.sensitiveWords);
        return;
      }

      // 从后端API获取敏感词列表
      wx.request({
        url: 'https://sunxiyue.com/zdh/api/feedback_api.php',
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: {
          action: 'get_sensitive_words_for_frontend'
        },
        success: (res) => {
          if (res.data && res.data.success && res.data.data) {
            this.sensitiveWords = res.data.data;
            this.isLoaded = true;
            
            // 缓存敏感词列表
            wx.setStorageSync('sensitive_words_cache', this.sensitiveWords);
            wx.setStorageSync('sensitive_words_cache_time', now);
            
            resolve(this.sensitiveWords);
          } else {
            // 如果API调用失败，使用默认敏感词列表
            this.sensitiveWords = this.getDefaultSensitiveWords();
            this.isLoaded = true;
            resolve(this.sensitiveWords);
          }
        },
        fail: (error) => {
          console.warn('获取敏感词列表失败，使用默认列表:', error);
          // 使用默认敏感词列表
          this.sensitiveWords = this.getDefaultSensitiveWords();
          this.isLoaded = true;
          resolve(this.sensitiveWords);
        }
      });
    });

    return this.loadPromise;
  }

  /**
   * 默认敏感词列表（与后端保持一致）
   */
  getDefaultSensitiveWords() {
    return [
      // 暴力相关
      '杀死', '杀害', '暴力', '打死', '弄死', '干死', '血腥', '残忍', '虐待', '折磨',

      // 脏话粗口
      '操', '草', '妈的', '他妈', '你妈', '傻逼', '傻B', '白痴', '智障', '脑残',
      '滚', '滚蛋', '去死', '死去', '找死', '该死', '混蛋', '王八蛋', '狗东西',

      // 非法活动
      '毒品', '吸毒', '贩毒', '制毒', '海洛因', '冰毒', '摇头丸', '大麻',
      '赌博', '赌场', '博彩', '六合彩', '私彩', '黑彩',
      '诈骗', '传销', '非法集资', '洗钱', '走私', '偷税', '逃税',

      // 色情相关
      '色情', '黄色', '淫秽', '裸体', '性交', '做爱', '嫖娼', '卖淫',

      // 政治敏感
      '法轮功', '邪教', '反政府', '颠覆', '暴动', '游行示威',

      // 其他不当内容
      '自杀', '自残', '跳楼', '割腕', '仇恨', '歧视', '种族主义'
    ];
  }

  /**
   * 检查文本是否包含敏感词
   * @param {string} text - 要检查的文本
   * @returns {Object} - 检查结果 {hasSensitiveWords: boolean, foundWords: Array, cleanText: string}
   */
  async checkText(text) {
    if (!text || typeof text !== 'string') {
      return {
        hasSensitiveWords: false,
        foundWords: [],
        cleanText: text || ''
      };
    }

    // 确保敏感词列表已加载
    if (!this.isLoaded) {
      await this.loadSensitiveWords();
    }

    const foundWords = [];
    let cleanText = text;

    // 检查每个敏感词
    for (const word of this.sensitiveWords) {
      if (text.toLowerCase().includes(word.toLowerCase())) {
        foundWords.push(word);
        // 用星号替换敏感词（保留首尾字符）
        const regex = new RegExp(this.escapeRegExp(word), 'gi');
        cleanText = cleanText.replace(regex, (match) => {
          if (match.length <= 2) {
            return '*'.repeat(match.length);
          }
          return match[0] + '*'.repeat(match.length - 2) + match[match.length - 1];
        });
      }
    }

    return {
      hasSensitiveWords: foundWords.length > 0,
      foundWords: foundWords,
      cleanText: cleanText
    };
  }

  /**
   * 转义正则表达式特殊字符
   */
  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 实时检查文本（用于输入时的即时反馈）
   * @param {string} text - 要检查的文本
   * @returns {Object} - 简化的检查结果
   */
  async quickCheck(text) {
    if (!text || typeof text !== 'string') {
      return { isValid: true, message: '' };
    }

    const result = await this.checkText(text);
    
    if (result.hasSensitiveWords) {
      return {
        isValid: false,
        message: `检测到不当词汇：${result.foundWords.slice(0, 3).join('、')}${result.foundWords.length > 3 ? '等' : ''}`,
        foundWords: result.foundWords
      };
    }

    return { isValid: true, message: '' };
  }

  /**
   * 清除缓存（用于强制刷新敏感词列表）
   */
  clearCache() {
    wx.removeStorageSync('sensitive_words_cache');
    wx.removeStorageSync('sensitive_words_cache_time');
    this.sensitiveWords = [];
    this.isLoaded = false;
    this.loadPromise = null;
  }
}

// 创建单例实例
const sensitiveWordFilter = new SensitiveWordFilter();

module.exports = {
  SensitiveWordFilter,
  sensitiveWordFilter
};
